<?php
use system\database;

//print_rr($input_params, 'input_params 1');

if (!isset($path) && !is_array($path)) $path = [];
//base paths
if (!isset($path['fs_app_root'])) $path['fs_app_root'] = '/'. tcs_path(str_replace("system", "", __DIR__));

$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
$path['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
$path['domain'] = $_SERVER['SERVER_NAME'] ?? '';
$path['script_name'] = $_SERVER['SCRIPT_NAME'];
$has_params = strpos($path['request_uri'], '?');
$path['request_uri'] = $has_params ? substr($_SERVER['REQUEST_URI'],0,$has_params) : $_SERVER['REQUEST_URI'] ?? '';

if (!isset($path['fs_doc_root'])) $path['fs_doc_root'] = '/' . tcs_path($_SERVER['DOCUMENT_ROOT']) . '/';
if (!isset($path['doc_root'])) $path['doc_root'] = '/'. tcs_path($_SERVER['DOCUMENT_ROOT']) . '/' ;

$path['fs_app'] = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/';

// fs_ paths

$directories = ['classes','functions','views','config','templates','uploads','api'];
foreach ($directories as $dir) {
    $path['fs_' . $dir] = $path['fs_app_root'] . 'resources/' . $dir . '/';

}
//app  paths
$path['app_root'] = '/' . tcs_path(str_replace($path['doc_root'], '', $path['fs_app_root'])) . '/';
$path['app_path'] = tcs_path(str_replace($path['app_root'], '',$path['request_uri'] ) );
$path['fs_app_path'] = '/' .tcs_path( $path['fs_views']  . '/' . $path['app_path'] . '/' );
$path['path_parts'] = explode('/', $path['app_path']);
$path['top_level'] = $path['path_parts'][0] ?? '';
$path['app_path'] = str_replace('get_view/', '/',$path['app_path']);
$path['current_page'] = '';
$path['fs_cache'] = '/var/www/vhosts/cadservices.co.uk/temp/autobooks';
$path['fs_temp'] = '/var/www/vhosts/cadservices.co.uk/temp/autobooks';


$path['set_by'] = 'default';
$path['source_path'] = '';
$path['source_page'] = '';
$path['source_app_path'] = '';


if (isset($_SERVER['HTTP_HX_CURRENT_URL'])) {
    $path['set_by'] = 'HTTP_HX_CURRENT_URL';
    $path['hx_current_url'] = $_SERVER['HTTP_HX_CURRENT_URL'];
    $path['hx_current_url_parts'] = parse_url($_SERVER['HTTP_HX_CURRENT_URL']);
    $path['source_path_parts'] = explode('/', tcs_path($path['hx_current_url_parts']['path']));
    $path['source_page'] = array_pop($path['source_path_parts']);
    $path['source_path'] = '/' . tcs_path(implode('/', $path['source_path_parts'])) . '/';
    $path['source_fs_path'] = '/' . tcs_path($path['fs_app_root'] . 'resources/views/' . $path['source_app_path']) . '/';
    $path['source_app_path'] = tcs_path(str_replace($path['app_root'], '', $path['source_path']));
}
if (count($path['path_parts']) > 1) {
    $last_element = count($path['path_parts']) - 1;
    $path['current_page'] = $path['path_parts'][$last_element];
} else {
    $path['current_page'] = $path['top_level'];
}
$path['current_page'] = explode('?', $path['current_page'])[0];
// Populate the constants

$path['fs_classes'] = $path['fs_app_root'] . 'resources/classes';
$path['fs_functions'] = $path['fs_app_root'] . 'resources/functions';
$path['fs_views'] = $path['fs_app_root'] . 'resources/views';
$path['fs_config'] = $path['fs_app_root'] . 'resources/config';
$path['fs_templates'] = $path['fs_app_root'] . 'resources/templates';
$path['fs_components'] = $path['fs_app_root'] . 'resources/components';
$path['fs_logs'] = $path['fs_app_root'] . 'logs/';


$path['fs_system'] = $path['fs_app_root'] . 'system/';
$path['fs_sys_db_class'] = '/'.tcs_path($path['fs_system'] . '/classes/database.class.php');
$path['fs_sys_classes'] = $path['fs_system'] . 'classes';
$path['fs_sys_functions'] = $path['fs_system'] . 'functions';
$path['fs_sys_views'] = $path['fs_system'] . 'views';
$path['fs_sys_config'] = $path['fs_system'] . 'config';
$path['fs_sys_templates'] = $path['fs_system'] . 'templates';
$path['fs_sys_components'] = $path['fs_system'] . 'components';
$path['fs_sys_logs'] = $path['fs_system'] . 'logs';


require_once $path['fs_sys_db_class'];

if (!defined('API_RUN') || !API_RUN) {
    $routes = database::table('autobooks_navigation as nav')
        ->select(['id', 'parent_path', 'route_key', 'name', 'icon', 'required_roles', 'show_navbar'])
        ->cast([
            'required_roles' => 'array',
            'show_navbar' => 'bool'
        ])
        ->get();
    $route_filter = function ($route) {
        if (in_array($route, ['icon', 'name', 'sub_folder'])) return false;
        return true;
    };
    define ('ROUTE_LIST', array_filter($routes, $route_filter, ARRAY_FILTER_USE_KEY ));
    define ('ROUTES', $routes);
    $nav_items = ROUTES;

}



if (!isset($input_params['action'])) {
    $temp = explode('/', $path['request_uri']);
    $temp2 = array_pop($temp);
    $input_params['action'] = $temp2;
}
function build_constants($path) {
    foreach ($path as $key => $value) {
        if (defined(strtoupper($key))) continue;
        if (is_string($value)) while (strpos($value, '//')) {
            $value = str_replace('//', '/', $value);
        }
        define(strtoupper($key), $value);
    }
}

function tcs_remove_keywords($path): string {
    $path = str_replace('get_view/', '/', $path);
    return $path;
}

function tcs_path($path): string {
    $path = str_replace('//', '/', $path);
    $path = trim(trim($path),'/');
    return $path;
}

function tcs_api($path): string {
    return '/' . APP_ROOT . 'api/' . $path;
}

?>
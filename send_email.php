<?php
// Enable error reporting for debugging
use autodesk_api\autodesk_api;
require_once 'system/startup_sequence_minimal.php';
// DEBUG_MODE and API_RUN are already defined in startup_sequence_minimal.php
error_reporting(E_ALL);
ini_set('display_errors', 1);
const LOGFILE = __DIR__ . '/logs/email_script.log';
const LOGFILE_MAX_FILESIZE = 10 * 1024 * 1024; // 10 MB

// Check if the file exists
if (file_exists(LOGFILE)) {
    // Get the file size
    $fileSize = filesize(LOGFILE);
    // Check if the file size is greater than 10 MB
    if ($fileSize > LOGFILE_MAX_FILESIZE) {
        // Delete the file
        unlink(LOGFILE);
    }
}


// Logging setup
if (!file_exists(LOGFILE) ) {
    $timestamp = (new DateTime())->format('Y-m-d H:i:s');
    file_put_contents(LOGFILE, "[$timestamp] logfile created" . PHP_EOL);

}
function log_message($message): string {
    $timestamp = (new DateTime())->format('Y-m-d H:i:s');
    $message = "[$timestamp] $message" . PHP_EOL;
    file_put_contents(LOGFILE, $message, FILE_APPEND);
    return $message;
}

$autodesk = new autodesk_api();
echo log_message('autodesk_api initialized.');

// Retrieve settings
$email_rules_string = autodesk_api::database_get_storage('subscription_renew_email_send_rules');
$email_rules = explode(',', $email_rules_string);
$settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
$settings_time = (int) autodesk_api::database_get_storage('subscription_renew_email_send_time');
echo log_message("Settings retrieved from database. Rules: {$email_rules_string}, Settings days: {$settings_days} and time: {$settings_time}");
// Check if today is a valid send day
$now = new DateTime();
$current_day_of_week = (int) $now->format('w');
if (!$settings_days[$current_day_of_week]) {
    die( log_message('Today is not a send day. Exiting script.'));
}

// Check if current time is within ±10 minutes of the send hour
$current_hour = (int) $now->format('G');
$current_minute = (int) $now->format('i');
$send_hour = $settings_time+1;
$send_minute_start = $send_hour * 60 - 10;
$send_minute_end = $send_hour * 60 + 10;
$current_time_in_minutes = $current_hour * 60 + $current_minute;

if ($current_time_in_minutes < $send_minute_start || $current_time_in_minutes > $send_minute_end) {
   //  die(log_message('Current time is not within the send time window. Exiting script.'));
}

echo log_message('Settings retrieved from database.');
echo log_message('Valid send time detected. Proceeding with email processing.');


// Get subscriptions and email template

$subs = $autodesk->subscriptions->get_renewable();
echo log_message(count($subs) . ' renewable subscriptions retrieved.');
$file_path = FS_APP_ROOT . 'resources/views/subscriptions/email_history/reminder_email.view.php';
$email_template = file_get_contents($file_path);

if ($email_template === false) {
    die(log_message("Failed to load email template from {$file_path}"));
}
echo log_message('Email template loaded successfully.');

$unsubs = $autodesk->get_unsubscribed();
$unsubs_referenceNums = array_column($unsubs, 'subscriptionReferenceNumber');
$unsubs_subIds = array_column($unsubs, 'subscriptionId');
// Process subscriptions
$count = 0;
foreach ($subs as $sub) {
    print_rr($sub);
    if ($sub['subs_tcs_unsubscribe'] == 1 || in_array($sub['subs_subscriptionReferenceNumber'], $unsubs_referenceNums) || in_array($sub['subs_subscriptionId'], $unsubs_subIds)) continue;
    $end_date = new DateTime($sub['subs_endDate']);
    $date_last_sent = $sub['hist_date'] ?? $sub['subs_startDate'];
    $last_sent = new DateTime($date_last_sent);
    $days_remaining = $now->diff($end_date)->days;
    if ($days_remaining < -30) continue;
    $days_since_last_sent = $now->diff($last_sent)->days;
    //start at lowest rule and work up
    foreach ($email_rules as $key => $rule) {
        if ($days_remaining <= $rule) {
            //when we get to a rule that has passed or is today process
            $days_to_send = $days_remaining + $days_since_last_sent;
            //when we get to a rule that has passed or is today, process it
            if ($days_to_send > $rule) {
                //if the rule plus days last sent is more than the rule then it was sent beyond the current rule and therefore not after this rule has passed
                $count++;
                $log_message = $sub['subs_subscriptionReferenceNumber'] . ": id " . $sub['subs_id'] . " for customer: " . $sub['endcust_name'] . " End date is " . $sub['subs_endDate'] . " with d/r: {$days_remaining} and last sent: {$days_since_last_sent} ({$sub['hist_date']}) so {$days_to_send} ({$days_remaining} + {$days_since_last_sent} is above rule $rule: not sending email to " . $sub['endcust_primary_admin_email'];
               // sleep(1);
                echo log_message($log_message);
               if ($count > 100) {
                   echo log_message('Reached the maximum number of emails to send in this session. Exiting loop.');
                   break 2;
               }
                $autodesk->subscriptions->send_reminder_email($sub, $rule, $sub['endcust_primary_admin_email']);
            }
            break;
        }
    }
}

echo log_message("Email sending process completed. {$count} emails sent.");
//echo ", there were {$count} emails sent.";
?>
